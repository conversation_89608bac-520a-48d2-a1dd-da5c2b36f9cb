using UnityEngine;
namespace Control
{
    public static class MeshGeneration
    {
        public static Mesh Rectangle(float width, float height, float cornerRadius)
        {
            Mesh mesh = new();

            // Ensure cornerRadius is not negative and not excessively large
            cornerRadius = Mathf.Max(0, cornerRadius);
            cornerRadius = Mathf.Min(cornerRadius, width / 2);
            cornerRadius = Mathf.Min(cornerRadius, height / 2);

            if (cornerRadius <= 1e-5f) // Treat as a sharp-cornered rectangle
            {
                Vector3[] rectVertices = new Vector3[4]
                {
                    new Vector3(-width / 2, -height / 2, 0), // Bottom-left
                    new Vector3(width / 2, -height / 2, 0),  // Bottom-right
                    new Vector3(width / 2, height / 2, 0),   // Top-right
                    new Vector3(-width / 2, height / 2, 0)   // Top-left
                };
                // Triangles for a quad (0,1,2 and 0,2,3 for CCW, Unity uses CW for front face by default with shaders)
                // To make it consistent with Unity's default quad (normals pointing away from camera if Z is depth)
                // Assuming standard view from +Z, CCW triangles are front. Normals (0,0,1)
                int[] rectTriangles = new int[] { 0, 2, 1, 0, 3, 2 };


                mesh.vertices = rectVertices;
                mesh.triangles = rectTriangles;
                mesh.RecalculateNormals();
                return mesh;
            }

            int cornerSegments = 8; // Number of segments for each 90-degree arc
            int numPointsPerCorner = cornerSegments + 1; // Including start and end points of the arc
            int numTotalPerimeterVertices = 4 * numPointsPerCorner;

            Vector3[] vertices = new Vector3[numTotalPerimeterVertices + 1]; // +1 for the center vertex
            vertices[0] = Vector3.zero; // Center vertex for fan triangulation
            int vertexIndex = 1; // Start populating perimeter vertices from index 1

            // Define corner properties: center X, center Y, start angle (deg), end angle (deg)
            // Order: Bottom-Left, Bottom-Right, Top-Right, Top-Left
            float[] cornerParams = new float[4 * 4];
            // BL
            cornerParams[0] = -width / 2 + cornerRadius; cornerParams[1] = -height / 2 + cornerRadius; cornerParams[2] = 180f; cornerParams[3] = 270f;
            // BR
            cornerParams[4] = width / 2 - cornerRadius; cornerParams[5] = -height / 2 + cornerRadius; cornerParams[6] = 270f; cornerParams[7] = 360f;
            // TR
            cornerParams[8] = width / 2 - cornerRadius; cornerParams[9] = height / 2 - cornerRadius; cornerParams[10] = 0f; cornerParams[11] = 90f;
            // TL
            cornerParams[12] = -width / 2 + cornerRadius; cornerParams[13] = height / 2 - cornerRadius; cornerParams[14] = 90f; cornerParams[15] = 180f;

            for (int i = 0; i < 4; i++) // Iterate through the four corners
            {
                float centerX = cornerParams[i * 4 + 0];
                float centerY = cornerParams[i * 4 + 1];
                float startAngleDeg = cornerParams[i * 4 + 2];
                float endAngleDeg = cornerParams[i * 4 + 3];

                for (int j = 0; j <= cornerSegments; j++) // Iterate through segments of a corner arc
                {
                    float t = (float)j / cornerSegments;
                    float angleRad = Mathf.Lerp(startAngleDeg, endAngleDeg, t) * Mathf.Deg2Rad;
                    vertices[vertexIndex++] = new Vector3(
                        centerX + cornerRadius * Mathf.Cos(angleRad),
                        centerY + cornerRadius * Mathf.Sin(angleRad),
                        0
                    );
                }
            }

            // Calculate triangles (fan triangulation from the center vertex)
            int[] triangles = new int[numTotalPerimeterVertices * 3];
            int triangleIndex = 0;

            for (int i = 0; i < numTotalPerimeterVertices; i++)
            {
                triangles[triangleIndex++] = 0; // Center vertex
                triangles[triangleIndex++] = i + 1; // Current perimeter vertex (offset by 1 due to center)
                // Next perimeter vertex, wrapping around to the first perimeter vertex
                triangles[triangleIndex++] = (i + 1) % numTotalPerimeterVertices + 1;
            }

            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.RecalculateNormals();

            return mesh;
        }

        public static Mesh Circle(float radius, int segments = 32, float startAngle = 0, float endAngle = 360)
        {
            Mesh mesh = new Mesh();
            Vector3[] vertices = new Vector3[segments + 1];
            int[] triangles = new int[segments * 3];

            float angleStep = (endAngle - startAngle) / segments;
            for (int i = 0; i <= segments; i++)
            {
                float angle = startAngle + i * angleStep;
                float radian = angle * Mathf.Deg2Rad;
                vertices[i] = new Vector3(radius * Mathf.Cos(radian), radius * Mathf.Sin(radian), 0);

                if (i < segments)
                {
                    triangles[i * 3] = 0; // Center vertex
                    triangles[i * 3 + 1] = i + 1;
                    triangles[i * 3 + 2] = i + 2 > segments ? 1 : i + 2; // Wrap around
                }
            }

            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.RecalculateNormals();

            return mesh;
        }
    }

    //TODO : Prompt this:
    /*
    Write me a class that allows me to create a Mesh Asset via the Unity Editor Window. 
    It should have a dropdown with the entries of each Mesh Generation function i define in the MeshGeneration class and should be easily extendable. 
    Whenever a MeshGeneration Function is choosen via the dropdown it should automatically expose the parameters as inputs and a Button to generate the Mesh Asset. 
    The default save Location should be the Assets folder. make sure that this script is a Unity Editor Script and shouldnt be included in a build
    */
}