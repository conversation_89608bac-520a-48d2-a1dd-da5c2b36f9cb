using UnityEngine;
using UnityEngine.InputSystem;
public class CardHandling : MonoBehaviour
{
    private Card selectedCard = null;
    private Camera cam;
    void Start()
    {
        cam = Camera.main;
    }
    public void OnClickDown()
    {
        RaycastHit hit;
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        Ray ray = cam.ScreenPointToRay(mousePosition);
        if (Physics.Raycast(ray, out hit))
        {   
            if (this.transform.TryGetComponent<Card>(out Card card))
            {
                selectedCard = hit.collider.GetComponent<Card>();
                Debug.Log("Card hit");
            }
        }

    }

    public void OnClickUp()
    {
        selectedCard = null;
    }

    public void OnMouseMove()
    {
        if (selectedCard != null)
        {
            Debug.Log("Dragging card: " + selectedCard.name);
            // Vector2 mousePosition = Mouse.current.position.ReadValue();
            // Vector3 worldPosition = cam.ScreenToWorldPoint(new Vector3(mousePosition.x, mousePosition.y, cam.nearClipPlane));
            // selectedCard.transform.position = new Vector3(worldPosition.x, worldPosition.y, selectedCard.transform.position.z);
        }
    }
}