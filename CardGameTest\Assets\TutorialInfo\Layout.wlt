%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12004, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PixelRect:
    serializedVersion: 2
    x: 0
    y: 45
    width: 1666
    height: 958
  m_ShowMode: 4
  m_Title: 
  m_RootView: {fileID: 6}
  m_MinSize: {x: 950, y: 542}
  m_MaxSize: {x: 10000, y: 10000}
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 466
    width: 290
    height: 442
  m_MinSize: {x: 234, y: 271}
  m_MaxSize: {x: 10004, y: 10021}
  m_ActualView: {fileID: 14}
  m_Panes:
  - {fileID: 14}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 4}
  - {fileID: 2}
  m_Position:
    serializedVersion: 2
    x: 973
    y: 0
    width: 290
    height: 908
  m_MinSize: {x: 234, y: 492}
  m_MaxSize: {x: 10004, y: 14042}
  vertical: 1
  controlID: 226
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 290
    height: 466
  m_MinSize: {x: 204, y: 221}
  m_MaxSize: {x: 4004, y: 4021}
  m_ActualView: {fileID: 17}
  m_Panes:
  - {fileID: 17}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 466
    width: 973
    height: 442
  m_MinSize: {x: 202, y: 221}
  m_MaxSize: {x: 4002, y: 4021}
  m_ActualView: {fileID: 15}
  m_Panes:
  - {fileID: 15}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12008, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 7}
  - {fileID: 8}
  - {fileID: 9}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1666
    height: 958
  m_MinSize: {x: 950, y: 542}
  m_MaxSize: {x: 10000, y: 10000}
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12011, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1666
    height: 30
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
  m_LastLoadedLayoutName: Tutorial
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 10}
  - {fileID: 3}
  - {fileID: 11}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 30
    width: 1666
    height: 908
  m_MinSize: {x: 713, y: 492}
  m_MaxSize: {x: 18008, y: 14042}
  vertical: 0
  controlID: 74
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12042, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 938
    width: 1666
    height: 20
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 12}
  - {fileID: 5}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 973
    height: 908
  m_MinSize: {x: 202, y: 442}
  m_MaxSize: {x: 4002, y: 8042}
  vertical: 1
  controlID: 75
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1263
    y: 0
    width: 403
    height: 908
  m_MinSize: {x: 277, y: 71}
  m_MaxSize: {x: 4002, y: 4021}
  m_ActualView: {fileID: 13}
  m_Panes:
  - {fileID: 13}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 973
    height: 466
  m_MinSize: {x: 202, y: 221}
  m_MaxSize: {x: 4002, y: 4021}
  m_ActualView: {fileID: 16}
  m_Panes:
  - {fileID: 16}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AutoRepaintOnSceneChange: 0
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -6905738622615590433, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
  m_DepthBufferBits: 0
  m_Pos:
    serializedVersion: 2
    x: 2
    y: 19
    width: 401
    height: 887
  m_ScrollPosition: {x: 0, y: 0}
  m_InspectorMode: 0
  m_PreviewResizer:
    m_CachedPref: -160
    m_ControlHash: -371814159
    m_PrefName: Preview_InspectorPreview
  m_PreviewWindow: {fileID: 0}
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AutoRepaintOnSceneChange: 0
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -7501376956915960154, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
  m_DepthBufferBits: 0
  m_Pos:
    serializedVersion: 2
    x: 2
    y: 19
    width: 286
    height: 421
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_VersionControlStates: []
    m_ReferencingInstanceIDs: 
    m_ScenePaths: []
    m_ShowAllHits: 0
    m_SearchArea: 0
    m_Folders:
    - Assets
  m_ViewMode: 0
  m_StartGridSize: 64
  m_LastFolders:
  - Assets
  m_LastFoldersGridSize: -1
  m_LastProjectPath: /Users/<USER>/Unity Projects/New Unity Project 47
  m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: ee240000
    m_LastClickedID: 9454
    m_ExpandedIDs: ee24000000ca9a3bffffff7f
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 68fbffff
    m_LastClickedID: 0
    m_ExpandedIDs: ee240000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 68fbffff
    m_LastClickedInstanceID: -1176
    m_HadKeyboardFocusLastEvent: 0
    m_ExpandedInstanceIDs: c6230000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_ClientGUIView: {fileID: 0}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 64
  m_DirectoriesAreaWidth: 110
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AutoRepaintOnSceneChange: 1
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -2087823869225018852, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
  m_DepthBufferBits: 32
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 19
    width: 971
    height: 421
  m_MaximizeOnPlay: 0
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 00000000000000000000000000000000000000000000000000000000000000000000000000000000
  m_TargetDisplay: 0
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    m_HBaseRangeMin: -242.75
    m_HBaseRangeMax: 242.75
    m_VBaseRangeMin: -101
    m_VBaseRangeMax: 101
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 1
    m_EnableSliderZoom: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 17
      width: 971
      height: 404
    m_Scale: {x: 2, y: 2}
    m_Translation: {x: 485.5, y: 202}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -242.75
      y: -101
      width: 485.5
      height: 202
    m_MinimalGUI: 1
  m_defaultScale: 2
  m_TargetTexture: {fileID: 0}
  m_CurrentColorSpace: 0
  m_LastWindowPixelSize: {x: 1942, y: 842}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000000000100000100
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AutoRepaintOnSceneChange: 1
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2318424515335265636, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
  m_DepthBufferBits: 32
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 19
    width: 971
    height: 445
  m_SceneLighting: 1
  lastFramingTime: 0
  m_2DMode: 0
  m_isRotationLocked: 0
  m_AudioPlay: 0
  m_Position:
    m_Target: {x: 0, y: 0, z: 0}
    speed: 2
    m_Value: {x: 0, y: 0, z: 0}
  m_RenderMode: 0
  m_ValidateTrueMetals: 0
  m_SceneViewState:
    showFog: 1
    showMaterialUpdate: 0
    showSkybox: 1
    showFlares: 1
    showImageEffects: 1
  grid:
    xGrid:
      m_Target: 0
      speed: 2
      m_Value: 0
    yGrid:
      m_Target: 1
      speed: 2
      m_Value: 1
    zGrid:
      m_Target: 0
      speed: 2
      m_Value: 0
  m_Rotation:
    m_Target: {x: -0.08717229, y: 0.89959055, z: -0.21045254, w: -0.3726226}
    speed: 2
    m_Value: {x: -0.08717229, y: 0.89959055, z: -0.21045254, w: -0.3726226}
  m_Size:
    m_Target: 10
    speed: 2
    m_Value: 10
  m_Ortho:
    m_Target: 0
    speed: 2
    m_Value: 0
  m_LastSceneViewRotation: {x: 0, y: 0, z: 0, w: 0}
  m_LastSceneViewOrtho: 0
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_LastLockedObject: {fileID: 0}
  m_ViewIsLockedToObject: 0
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AutoRepaintOnSceneChange: 0
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: -590624980919486359, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
  m_DepthBufferBits: 0
  m_Pos:
    serializedVersion: 2
    x: 2
    y: 19
    width: 286
    height: 445
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 68fbffff
    m_LastClickedID: -1176
    m_ExpandedIDs: 7efbffff00000000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
  m_ExpandedScenes:
  - 
  m_CurrenRootInstanceID: 0
  m_Locked: 0
  m_CurrentSortingName: TransformSorting
