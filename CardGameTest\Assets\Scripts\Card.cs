using JetBrains.Rider.Unity.Editor;
using UnityEngine;
using Control;

[RequireComponent(typeof(Rigidbody), typeof(Collider))]
public class Card : MonoBehaviour
{
    public Rigidbody RB
    {
        get
        {
            if (rb == null)
            {
                rb = transform.GetOrAdd<Rigidbody>();
            }
            return rb;
        }
    }
    private Rigidbody rb;

    private Collider col;

    void Start()
    {
        rb = this.transform.GetOrAdd<Rigidbody>();
        col = this.transform.GetComponent<Collider>();
    }

}